'use client';

import { useState } from 'react';
import { DbTool } from '@/lib/types';

interface EnhancedToolsTableProps {
  tools: DbTool[];
  loading: boolean;
  selectedTools: string[];
  onSelectionChange: (selectedIds: string[]) => void;
  onToolUpdate: (toolId: string, updates: Partial<DbTool>) => void;
  onToolDelete: (toolId: string) => void;
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
}

export function EnhancedToolsTable({
  tools,
  loading,
  selectedTools,
  onSelectionChange,
  onToolUpdate,
  onToolDelete,
  currentPage,
  totalPages,
  onPageChange
}: EnhancedToolsTableProps) {
  const [expandedRows, setExpandedRows] = useState<Set<string>>(new Set());

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      onSelectionChange(tools.map(tool => tool.id));
    } else {
      onSelectionChange([]);
    }
  };

  const handleSelectTool = (toolId: string, checked: boolean) => {
    if (checked) {
      onSelectionChange([...selectedTools, toolId]);
    } else {
      onSelectionChange(selectedTools.filter(id => id !== toolId));
    }
  };

  const toggleRowExpansion = (toolId: string) => {
    const newExpanded = new Set(expandedRows);
    if (newExpanded.has(toolId)) {
      newExpanded.delete(toolId);
    } else {
      newExpanded.add(toolId);
    }
    setExpandedRows(newExpanded);
  };

  const handleStatusChange = (toolId: string, newStatus: string) => {
    const updates: Partial<DbTool> = {
      content_status: newStatus as any,
      updated_at: new Date().toISOString()
    };

    if (newStatus === 'published' && !tools.find(t => t.id === toolId)?.published_at) {
      updates.published_at = new Date().toISOString();
    }

    onToolUpdate(toolId, updates);
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getStatusBadge = (status: string) => {
    const statusColors = {
      published: 'bg-green-600',
      draft: 'bg-yellow-600',
      archived: 'bg-gray-600',
      under_review: 'bg-blue-600'
    };

    return (
      <span className={`px-2 py-1 text-xs rounded-full text-white ${statusColors[status] || 'bg-gray-600'}`}>
        {status}
      </span>
    );
  };

  const getSubmissionTypeBadge = (type: string) => {
    const typeColors = {
      simple: 'bg-purple-600',
      detailed: 'bg-orange-600',
      admin: 'bg-blue-600'
    };

    const typeLabels = {
      simple: 'AI Generated',
      detailed: 'User Provided',
      admin: 'Admin Created'
    };

    return (
      <span className={`px-2 py-1 text-xs rounded-full text-white ${typeColors[type] || 'bg-gray-600'}`}>
        {typeLabels[type] || type}
      </span>
    );
  };

  if (loading) {
    return (
      <div className="bg-zinc-800 border border-zinc-700 rounded-lg p-8">
        <div className="flex items-center justify-center">
          <div className="animate-spin w-8 h-8 border-4 border-orange-500 border-t-transparent rounded-full"></div>
          <span className="ml-3 text-gray-300">Loading tools...</span>
        </div>
      </div>
    );
  }

  if (tools.length === 0) {
    return (
      <div className="bg-zinc-800 border border-zinc-700 rounded-lg p-8 text-center">
        <p className="text-gray-400">No tools found matching your criteria.</p>
      </div>
    );
  }

  return (
    <div className="bg-zinc-800 border border-zinc-700 rounded-lg overflow-hidden">
      {/* Table Header */}
      <div className="overflow-x-auto">
        <table className="w-full">
          <thead className="bg-zinc-700">
            <tr>
              <th className="px-4 py-3 text-left">
                <input
                  type="checkbox"
                  checked={selectedTools.length === tools.length && tools.length > 0}
                  onChange={(e) => handleSelectAll(e.target.checked)}
                  className="rounded border-zinc-600 bg-zinc-700 text-orange-500 focus:ring-orange-500"
                />
              </th>
              <th className="px-4 py-3 text-left text-sm font-medium text-gray-300">Tool</th>
              <th className="px-4 py-3 text-left text-sm font-medium text-gray-300">Status</th>
              <th className="px-4 py-3 text-left text-sm font-medium text-gray-300">Type</th>
              <th className="px-4 py-3 text-left text-sm font-medium text-gray-300">Submitted</th>
              <th className="px-4 py-3 text-left text-sm font-medium text-gray-300">Media</th>
              <th className="px-4 py-3 text-left text-sm font-medium text-gray-300">Actions</th>
            </tr>
          </thead>
          <tbody className="divide-y divide-zinc-700">
            {tools.map((tool) => (
              <tr key={tool.id} className="hover:bg-zinc-700/50">
                <td className="px-4 py-3">
                  <input
                    type="checkbox"
                    checked={selectedTools.includes(tool.id)}
                    onChange={(e) => handleSelectTool(tool.id, e.target.checked)}
                    className="rounded border-zinc-600 bg-zinc-700 text-orange-500 focus:ring-orange-500"
                  />
                </td>
                <td className="px-4 py-3">
                  <div className="flex items-center gap-3">
                    {/* Media Preview with Error Handling */}
                    <div className="flex-shrink-0">
                      {tool.primary_image || tool.logo_url ? (
                        <img
                          src={tool.primary_image || tool.logo_url || ''}
                          alt={tool.name}
                          className="w-10 h-10 rounded object-cover bg-zinc-600"
                          onError={(e) => {
                            // Fallback to placeholder on error
                            e.currentTarget.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQwIiBoZWlnaHQ9IjQwIiBmaWxsPSIjNEY0RjRGIi8+Cjx0ZXh0IHg9IjIwIiB5PSIyNCIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjEyIiBmaWxsPSIjOTk5OTk5IiB0ZXh0LWFuY2hvcj0ibWlkZGxlIj7wn5OPPC90ZXh0Pgo8L3N2Zz4K';
                          }}
                        />
                      ) : (
                        <div className="w-10 h-10 rounded bg-zinc-600 flex items-center justify-center">
                          <span className="text-xs text-gray-400">📷</span>
                        </div>
                      )}
                    </div>
                    
                    <div className="min-w-0 flex-1">
                      <div className="flex items-center gap-2">
                        <h3 className="text-sm font-medium text-white truncate">{tool.name}</h3>
                        {tool.is_verified && (
                          <span className="text-green-400 text-xs">✓</span>
                        )}
                        <button
                          onClick={() => toggleRowExpansion(tool.id)}
                          className="text-gray-400 hover:text-white text-xs"
                        >
                          {expandedRows.has(tool.id) ? '▼' : '▶'}
                        </button>
                      </div>
                      <p className="text-xs text-gray-400 truncate">{tool.description}</p>
                      {tool.submitted_by && (
                        <p className="text-xs text-gray-500">by {tool.submitted_by}</p>
                      )}
                      {/* Enhanced Fields Display */}
                      {expandedRows.has(tool.id) && (
                        <div className="mt-2 text-xs text-gray-400 space-y-1">
                          {tool.use_cases && (
                            <div><strong>Use Cases:</strong> {tool.use_cases.substring(0, 100)}...</div>
                          )}
                          {tool.target_audience && (
                            <div><strong>Target:</strong> {tool.target_audience.substring(0, 80)}...</div>
                          )}
                          {tool.tags && tool.tags.length > 0 && (
                            <div><strong>Tags:</strong> {tool.tags.join(', ')}</div>
                          )}
                          {tool.integrations && (
                            <div><strong>Integrations:</strong> {tool.integrations.substring(0, 80)}...</div>
                          )}
                        </div>
                      )}
                    </div>
                  </div>
                </td>
                <td className="px-4 py-3">
                  <select
                    value={tool.content_status}
                    onChange={(e) => handleStatusChange(tool.id, e.target.value)}
                    className="text-xs bg-zinc-700 border border-zinc-600 rounded px-2 py-1 text-white"
                  >
                    <option value="draft">Draft</option>
                    <option value="published">Published</option>
                    <option value="archived">Archived</option>
                    <option value="under_review">Under Review</option>
                  </select>
                </td>
                <td className="px-4 py-3">
                  {getSubmissionTypeBadge(tool.submission_type || 'admin')}
                </td>
                <td className="px-4 py-3">
                  <div className="text-xs text-gray-300">
                    <div>{formatDate(tool.submission_date || tool.created_at)}</div>
                    {tool.approved_by && (
                      <div className="text-gray-500">by {tool.approved_by}</div>
                    )}
                  </div>
                </td>
                <td className="px-4 py-3">
                  <div className="flex items-center gap-1 text-xs">
                    {tool.primary_image && <span className="text-green-400">📸</span>}
                    {tool.logo_url && <span className="text-blue-400">🎨</span>}
                    {!tool.primary_image && !tool.logo_url && (
                      <span className="text-gray-500">❌</span>
                    )}
                  </div>
                </td>
                <td className="px-4 py-3">
                  <div className="flex items-center gap-2">
                    <a
                      href={tool.website}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-xs text-blue-400 hover:text-blue-300"
                    >
                      Visit
                    </a>
                    <button
                      onClick={() => onToolDelete(tool.id)}
                      className="text-xs text-red-400 hover:text-red-300"
                    >
                      Delete
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="px-4 py-3 border-t border-zinc-700 flex items-center justify-between">
          <div className="text-sm text-gray-400">
            Page {currentPage} of {totalPages}
          </div>
          <div className="flex items-center gap-2">
            <button
              onClick={() => onPageChange(currentPage - 1)}
              disabled={currentPage === 1}
              className="px-3 py-1 text-sm bg-zinc-700 hover:bg-zinc-600 disabled:opacity-50 disabled:cursor-not-allowed rounded"
            >
              Previous
            </button>
            <button
              onClick={() => onPageChange(currentPage + 1)}
              disabled={currentPage === totalPages}
              className="px-3 py-1 text-sm bg-zinc-700 hover:bg-zinc-600 disabled:opacity-50 disabled:cursor-not-allowed rounded"
            >
              Next
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
